import { Given, Then, When } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { pageFixture } from '../../../../../config/pageFixtures';
import { NationalEventCreationPage } from '../../../PageObjectModel/EventCreation/National/NationalEventCreationPage';
import { attemptLogin } from '../../../Utils/loginHelper';
import { NationalEventsOfferCreationSectionPage } from '../../../PageObjectModel/OfferCreation/NationalEventsOfferCreationSectionPage';

 let nationalEventCreationPage: NationalEventCreationPage;
let nationalEventsOfferCreationSectionPage: NationalEventsOfferCreationSectionPage;

const env = process.env.TEST_ENV || "qa2";

Given("{string} logs into MEUPP application", { timeout: 120 * 1000 }, async function (userRole: string) {
    await attemptLogin(env, pageFixture.page,userRole);
});

When(`User clicks on 'Create Event' in the 'Promotion Management' page`, async () => {
  nationalEventsOfferCreationSectionPage = new NationalEventsOfferCreationSectionPage(pageFixture.page);
  await nationalEventsOfferCreationSectionPage.userClicksOnCreateEvent();
});


When(`User select the event type {string}`, async (arg0: string) => {
    await nationalEventsOfferCreationSectionPage.userClicksOnGetStartedForDivisionPromotion();
});

Then('User populates the product group with {string}', async function (ppg) {
    console.log(`Received PPG from Examples table: ${ppg}`);
    await nationalEventsOfferCreationSectionPage.userClicksOnPPG(ppg);
});

When('User selects vehicle type {string}', async (vehicleType: string) => {
    await nationalEventsOfferCreationSectionPage.userSelectsVehicleType(vehicleType)
});

When('User sets event date as {string}', async (eventDate: string) => {
    await nationalEventsOfferCreationSectionPage.userSetsEventDate(eventDate);
});

When(`User save the event details`, async () => {
    await nationalEventsOfferCreationSectionPage.userSaveEventDetails();
});

When(`User Remove Invalid divisions if exists`, async () => {
    await nationalEventsOfferCreationSectionPage.userRemoveInvalidDivisions();
});

Then('Verify the user can save the event details successfully', async () => {
    await nationalEventsOfferCreationSectionPage.verifyEventDetailsAreSaved();
});

When("User clicks on the Division count displayed within the 'Event Details' section", async function() {
    nationalEventCreationPage = new NationalEventCreationPage(pageFixture.page);
    await nationalEventCreationPage.clickDivisionCount();
});

Then("User captures the list of divisions for the national event", async function() {
    const divisions = await nationalEventCreationPage.getDivisionsList();
    console.log('Retrieved divisions:', divisions);
    // You can add assertions here if needed
    expect(divisions.length).toBeGreaterThan(0);
});

Then("User gets the list of divisions from the breakdown table", async function() {
    await nationalEventCreationPage.getDivisionsFromTable();
});

Then("User verifies that divisions in popper match divisions in the table", async function() {
    await nationalEventCreationPage.compareDivisionLists();
});

When('User clicks on the PID input field', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.clickPIDInput();
});

When('User enters a valid PID {string} in the PID input field', async function (PID: string) {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.fillPID(PID);
});

When('User clicks on "Fetch Event Details" button beside PID input field', async () => {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.clickFetchEventDetails();
});

Then('verify the stores attached to the PID {string} should be displayed', async function (expectedStores: string) {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyStoreGroupsSelected(expectedStores);
});

Then('the promo product group search box should be visible', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyPromoProductGroupSearchBoxVisible();
});

Then('the promo product group search box should be enabled', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyPromoProductGroupSearchBoxEnabled();
});

Then('the product group name should be visible', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyProductGroupNameVisible();
});

Then('the store group type button should be visible', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyStoreGroupTypeButtonVisible();
});

Then('the store group type button should be enabled', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyStoreGroupTypeButtonEnabled();
});

Then('the selection dropdown button should be visible', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifySelectionDropdownButtonVisible();
});

Then('the selection dropdown button should be enabled', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifySelectionDropdownButtonEnabled();
});

Then('the vehicle type button should be visible', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyVehicleTypeButtonVisible();
});

Then('the vehicle type button should be enabled', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyVehicleTypeButtonEnabled();
});

Then('the start week button should be visible', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyStartWeekButtonVisible();
});

Then('the start week button should be enabled', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyStartWeekButtonEnabled();
});

Then('the event name input should be visible', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyEventNameInputVisible();
});

Then('the event name input should not be empty', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyEventNameInputNotEmpty();
});

When('User manually select store groups {string}', async function (selectableStoreGroups: string) {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.selectStoreGroups(selectableStoreGroups);
});

Then('the selected store groups {string} should be displayed', async function (selectableStoreGroups: string) {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyStoreGroupsSelected(selectableStoreGroups);
});

Then('No warning message should be displayed', async function () {
  const page = new NationalEventCreationPage(pageFixture.page);
  await page.verifyNoWarningMessageDisplayed();
});

When(
  /^User deselects "([^"]*)" division from the 'Store Groups\*' dropdown in the 'Event Details' section$/,
  async function (divisionName) {
    await nationalEventsOfferCreationSectionPage.userDeSelectsDivision(divisionName);
  }
);
