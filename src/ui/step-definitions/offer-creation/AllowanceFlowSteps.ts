import { Then, When } from '@cucumber/cucumber';
import { pageFixture } from '../../../../config/pageFixtures';
import { NationalEventsOfferCreationSectionPage } from '../../PageObjectModel/OfferCreation/NationalEventsOfferCreationSectionPage';

let nationalEventsOfferCreationSectionPage: NationalEventsOfferCreationSectionPage;

When('User click on {string}', async (allowanceType: string) => {
   console.log("User going to click on "+allowanceType);
   nationalEventsOfferCreationSectionPage = new NationalEventsOfferCreationSectionPage(pageFixture.page);
   // nationalEventsOfferCreationSection = new NationalEventsOfferCreationSection(pageFixture.page);
   await nationalEventsOfferCreationSectionPage.clickOnAllowanceTypeDropdown();
   await nationalEventsOfferCreationSectionPage.selectOption(allowanceType);
});

Then('User selects the {string}', async function (performanceType) {
   await nationalEventsOfferCreationSectionPage.selectPerformanceOption(performanceType);
});

When('User clicks on Enter amounts', async function () {
   await nationalEventsOfferCreationSectionPage.clickEnterAmountsButton();
});

When("User clicks on update offer button", async function () {
   await nationalEventsOfferCreationSectionPage.UserClickOnUpdateOffer();
});

When(`User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen`, async function () {
   const nationalEventsOfferCreationSectionPage = new NationalEventsOfferCreationSectionPage(pageFixture.page);
   await nationalEventsOfferCreationSectionPage.userClicksUpdateAllFieldsTextBoxInAllowanceAmountsMainEntryScreen();
});

When(
    /^User enters "([^"]*)" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen$/,
    async function (amount: string) {
      const nationalEventsOfferCreationSectionPage = new NationalEventsOfferCreationSectionPage(pageFixture.page);
        await nationalEventsOfferCreationSectionPage.userEntersAllowanceAmountToUpdateAllFieldsTextBoxInAllowanceAmountsMainEntryScreen(amount);
    }
);

When(
    /^User validates the allowance amount "([^"]*)" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen$/,
    async function (amount: string) {
      const nationalEventsOfferCreationSectionPage = new NationalEventsOfferCreationSectionPage(pageFixture.page);
        await nationalEventsOfferCreationSectionPage.userValidatesTheDisplayOfTheAllowanceAmountEnteredToTheUpdateAllFieldsTextBoxInAllowanceAmountsMainEntryScreen(amount);
    }
);

When('User click on Update All divisions', async function () {
   await nationalEventsOfferCreationSectionPage.UpdateAllDivisionsButton();
});

Then('User click on Save all changes', async function () {
   await nationalEventsOfferCreationSectionPage.UserClickOnSaveAllChanges();
});

Then('User click on Save and Create Allowance', async function () {
   await nationalEventsOfferCreationSectionPage.UserClickOnSaveAndCreateAllowance();
});

Then('verify Allowance to be created field value in the preview header', async function () {
   await nationalEventsOfferCreationSectionPage.VerifyTheValueInPreviewHeader();
});

 When('User clicks on header flat allowance amount text box', async function () {
   await nationalEventsOfferCreationSectionPage.clickHeaderFlatAllowanceAmountTextBox();
 });

 When('User clicks on the header flat allowance amount', async function () {
   await nationalEventsOfferCreationSectionPage.clickHeaderFlatAllowanceAmountTextBoxDefault();
 });

Then('Verify {string} and {string} is visible', async function (myVendorText: string, otherVendorText: string) {
   await nationalEventsOfferCreationSectionPage.VerifyTheValueInPreviewHeader();
});

Then(`User clicks the checkbox preceding the text, 'I will submit Allowances for all Stores & Items, including those delivered by other Vendors and I will be funding them.'`, async function () {
   await nationalEventsOfferCreationSectionPage.UserShouldClickOnTheCheckboxOfTheRadioButton();
});

Then('Verify {string} and {string} headers are displayed in the dates stepper', async function (myVendorText: string, otherVendorText: string) {
   await nationalEventsOfferCreationSectionPage.VerifyTheVisibilityOfVendor(myVendorText, otherVendorText);
});

Then('User select the {string}', async function (option) {
   await nationalEventsOfferCreationSectionPage.UserShouldSelectTheLeadDistributor();
});

Then('User click on the {string}', async function (buttonText) {
   await nationalEventsOfferCreationSectionPage.UserShouldSelectTheLeadDistributorCheckbox();
});

Then('User will click on Confirm', async function () {
   await nationalEventsOfferCreationSectionPage.UserWillClickOnConfirm('Confirm');
});

Then('User selects on {string}', async function (buttonText) {
   await nationalEventsOfferCreationSectionPage.UserWillClickOnViewSummary();
});

Then('User verify the Lead distributor in View summary page for National Vendor', async function () {
   await nationalEventsOfferCreationSectionPage.VerifyTheLeadDistributorInSummaryPage();
});

Then('User will check the Lead Distributor for Haggen division', async function () {
    await nationalEventsOfferCreationSectionPage.UserWillCheckTheLeadDistributorForHaggendivision();
});

Then('User will check the Lead Distributor for Socal division', async function () {
    await nationalEventsOfferCreationSectionPage.UserWillCheckTheLeadDistributorForSocaldivision();
});

Then(`User will click on Edit Division Billing Info`, async function () {
   await nationalEventsOfferCreationSectionPage.UserWillCheckOnEditDivisionBillingInfo();
});
Then(`Verify the Lead Distributor for Haggen division`, async function() {
   await nationalEventsOfferCreationSectionPage.VerifyTheLeadForHaggen();
});

Then(`Verify the Lead Distributor for Socal division`, async function() {
   await nationalEventsOfferCreationSectionPage.VerifyTheLeadForSocal();
});

Then('User click on Expand All',async function(){
   await nationalEventsOfferCreationSectionPage.UserClickOnExpandAll();
});
Then('Verify whether the results stored in the array are displayed as the distributor list in the dates stepper', async function () {
   await nationalEventsOfferCreationSectionPage.VerifyTheDistributorList();
});

Then(`Verify the Lead selection for multivendor PPG`,async function () {
  await nationalEventsOfferCreationSectionPage.UserVerifyTheLeadSelection();
});

Then('User will click on Edit Divison Dates', async function(){
   await nationalEventsOfferCreationSectionPage.UserClickOnEditDivisionDates();
});

Then('Verify the grayed out field for multivendor PPG', async function () {
    await nationalEventsOfferCreationSectionPage.VerifyTheGrayedOutFieldForMultivendorppg();
});

Then('Verify user should not see List cost , master cost in amount screen',async function(){
   await nationalEventsOfferCreationSectionPage.VerifyUserShouldNotSeeListCostMasterCost();
});

Then('Verify user should see listcost,mastercost in amount screen for My Vendors', async function(){
    await nationalEventsOfferCreationSectionPage.VerifyUserShouldSeeListCostMasterCostForMyVendors();
});
When('User click on Socal division for multivendor flow',async function(){
   await nationalEventsOfferCreationSectionPage.VerifyUserShouldClickOnSocalDivisionForMultiVendor();
});
 
 When('User click on continue to Billing Details', async function () {
   await nationalEventsOfferCreationSectionPage.clickContinueToBillingDetails();
 });
Then("User verifies whether the offer's status is {string}", async function(expectedStatus: string) {
   const nationalEventsOfferCreationSection = new NationalEventsOfferCreationSectionPage(pageFixture.page);
   await nationalEventsOfferCreationSection.verifyOfferStatus(expectedStatus);
});

Then("User verifies the display of the {string} for 'Canceled' or 'Rejected' offers in the 'Offers' tab", async function(status: string) {
   const nationalEventsOfferCreationSection = new NationalEventsOfferCreationSectionPage(pageFixture.page);
   await nationalEventsOfferCreationSection.verifyCanceledOrRejectedStatusInOfferTabSection(status);
});

When("User clicks on the {string} option displayed on the offer's collapsed panel", async function (actionType: string) {
  const nationalEventsOfferCreationSection = new NationalEventsOfferCreationSectionPage(pageFixture.page);
  await nationalEventsOfferCreationSection.clickActionButton(actionType);
});

Then("User verifies the confirmation modal is visible for {string} action", async function (actionType: string) {
  const nationalEventsOfferCreationSection = new NationalEventsOfferCreationSectionPage(pageFixture.page);
  await nationalEventsOfferCreationSection.verifyConfirmationModalVisible();
});

Then("User verifies the confirmation message text is visible for {string} action", async function (actionType: string) {
  const nationalEventsOfferCreationSection = new NationalEventsOfferCreationSectionPage(pageFixture.page);
  await nationalEventsOfferCreationSection.verifyConfirmationMessageVisible();
});

When("User confirms the {string} action on the confirmation popup", async function (actionType: string) {
  const nationalEventsOfferCreationSection = new NationalEventsOfferCreationSectionPage(pageFixture.page);
  await nationalEventsOfferCreationSection.UserWillClickOnConfirm(actionType);
});

When("User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page", async function () {
  const nationalEventsOfferCreationSection = new NationalEventsOfferCreationSectionPage(pageFixture.page);
  await nationalEventsOfferCreationSection.clickAddAnotherOffer();
});

When("User selects {string} radio button in the first stepper of the allowance workflow section", async function (radioButtonLabel: string) {
  const nationalEventsOfferCreationSection = new NationalEventsOfferCreationSectionPage(pageFixture.page);
  await nationalEventsOfferCreationSection.selectRadioButtonInTheFirstStepperOfAllowanceWorkflowSection(radioButtonLabel);
});

When('User enters {string} to the header flat allowance amount text box in the first stepper of the allowance workflow section', async function (amount: string) {
   await nationalEventsOfferCreationSectionPage.enterHeaderFlatAllowanceAmount(amount);
});
When ('user clicks on review overlaps and enter amounts button', async function () {
   await nationalEventsOfferCreationSectionPage.clickReviewOverlapsAndEnterAmountsButton();
   
});



  Then('user clicks on {string}',  async function () {
    // Write code here that turns the phrase above into concrete actions
    const nationalEventsOfferCreationSection = new NationalEventsOfferCreationSectionPage(pageFixture.page);
   await nationalEventsOfferCreationSection.clickEditViewAllItemsButton();
    return 'pending';
  });

 