import { Page, expect, Locator } from "@playwright/test";
import { NationalEventsOfferCreationSectionPageEnum } from "../../Enums/NationalEventsOfferCreationSectionPageEnum";
import { getVehicleInsertValueString } from "../../Utils/CommonUtils";
import { MongoDBComponent } from "../../Components/MongoDBComponent/MongoDBComponent";

const env = process.env.TEST_ENV || "qa2";
const mongoDBComponent = new MongoDBComponent(env);

export class NationalEventsOfferCreationSectionPage {


    readonly page: Page;
    readonly offerHeader: Locator;
    readonly allowanceTypeDropdown: Locator;
    readonly allowanceToBeCreatedContentInputLabelForCaseOfferFlow: Locator;
    readonly allowanceDatesHeader: Locator;
    readonly warehouseAllowanceDatesHeader: Locator;
    readonly dsdAllowanceDatesHeader: Locator;
    readonly bothRadioButton: Locator;
    readonly warehouseOnlyRadioButton: Locator;
    readonly dsdOnlyRadioButton: Locator;
    readonly warehouseOnlyRadioButtonDefaultCheckedAndDisabled: Locator;
    readonly combinedDsdRadioButton: Locator;
    readonly separateAllowancesByDsdRadioButton: Locator;
    readonly okButtonOnAllowAmountMainEntryScreenOneTimePopupMessage: Locator;
    readonly enterAmountsButton: Locator;
    readonly allowanceMainEntryScreenAmountsTextBox: Locator;
    readonly updateAllDivisionsButton: Locator;
    readonly saveAllChangesButton: Locator;
    readonly continueToWarehouseDatesButton: Locator;
    readonly saveAndCreateAllowancesButton: Locator;
    readonly reviewOverlapsAndEnterAmountsButton: Locator;
    readonly allowanceToBeCreatedFieldsValue: Locator;
    readonly viewOfferDealSheetButton: Locator;
    readonly viewSummaryButton: Locator;
    readonly headerFlatAllowanceAmountTextBox: Locator;
    readonly continueToBillingDetails: Locator;
    readonly headerFlatAllowanceTypeValue: Locator;
    readonly viewSummaryModalHeader: Locator;
    readonly shipStore: Locator;
    readonly oneAllowanceWarehouseDSDradioOption: Locator;
    readonly separateAllowancesByDSDradioOption: Locator;
    readonly defaultSelectValueDisplayInPerformanceDropdown: Locator;   
    readonly fourUEventPerformanceValue: Locator;
    readonly addAnotherOfferButtonLocator: Locator;
    readonly addEvent: Locator;
    readonly createEventLocator: Locator;
    readonly getStartedNational: Locator;
    readonly getStartedDivisionPromotion: Locator;
    readonly getStartedAllowanceOnly: Locator;
    
    readonly ppgDropdown: Locator;
    readonly searchPPG: Locator;
    readonly saveEventDetails: Locator;
    readonly removeInvalidDivisions: Locator;
    readonly eventId: Locator;
    readonly selectsVehicleType: Locator;
    readonly selectStartWeekVehicle: Locator;
    readonly mutivendorCheckbox: Locator;
    readonly leadDistributorCheckbox: Locator;
    readonly leadDistributor1: Locator;
    readonly leadDistributor2: Locator;
    readonly confirm: Locator;
    readonly viewSummary: Locator;
    readonly socal: Locator;
    readonly EditDivisionBillingInfo: Locator;
    readonly haggen: Locator;
    readonly socalBilling: Locator;
    readonly expandAll: Locator;
    readonly editDivisionDateLocator: Locator;
    readonly unitListCostLocator: Locator;
    readonly unitCostWithAllowLocator: Locator;
    readonly costLocator: Locator;
    readonly masterCostLocator: Locator;
    readonly listCostForMyVendorLocator: Locator;
    readonly masterCostForMyVendorLocator: Locator;
    readonly dataVisibilityForMyVendor: Locator;
    readonly offerStatusLocator: Locator;
    readonly offerTabLocator: Locator;
    readonly divisionsSelectedTextLocator: Locator;
    readonly offersStatusesLocator: Locator;
    readonly cancelButtonLocator: Locator;
    readonly editButtonLocator: Locator;
    readonly modalLocator: Locator;
    readonly loaderLocator: Locator;
  readonly editviewallitembutton:Locator;
  


  constructor(page: Page) {
    this.page = page;
    this.offerHeader = this.page.getByText("New Offer");
    this.allowanceTypeDropdown = this.page.locator(
      '//div[@id="abs-input-select-container"]/button[@name="allowanceType"]'
    );
    this.allowanceToBeCreatedContentInputLabelForCaseOfferFlow =
      this.page.locator(
        "[data-testid='allowance-to-be-created-content-input'] label"
      );
    this.allowanceDatesHeader = this.page.getByText("Allowance Dates");
    this.warehouseAllowanceDatesHeader = this.page.getByText(
      "Warehouse Allowance Dates"
    );
    this.dsdAllowanceDatesHeader = this.page.getByText("DSD Allowance Dates");
    this.bothRadioButton = this.page
      .locator('//span[@data-testid="radio-check"]')
      .nth(0);
    this.warehouseOnlyRadioButton = this.page
      .locator('//span[@data-testid="radio-check"]')
      .nth(1);
    this.dsdOnlyRadioButton = this.page
      .locator('//span[@data-testid="radio-check"]')
      .nth(2);
    this.warehouseOnlyRadioButtonDefaultCheckedAndDisabled =
      this.page.getByTestId("radio-check");
    this.combinedDsdRadioButton = this.page
      .locator("label")
      .filter({ hasText: "Combined DSD" })
      .getByTestId("radio-check");
    this.separateAllowancesByDsdRadioButton = this.page
      .locator("label")
      .filter({ hasText: "Separate Allowances By DSD" })
      .getByTestId("radio-check");
    this.okButtonOnAllowAmountMainEntryScreenOneTimePopupMessage =
      this.page.locator(`//button[text()="OK"]`);
    this.enterAmountsButton = this.page.getByRole("button", {
      name: "Enter Amounts",
    });
    this.allowanceMainEntryScreenAmountsTextBox =
      this.page.getByRole("spinbutton");
        this.enterAmountsButton = this.page.locator('//div[@data-testid="abs-allowance-form-wrapper-label-cont"]//button[text()="Enter Amounts"]'); // //div[@data-testid="abs-allowance-form-wrapper-label-cont"]//button[text()="Enter Amounts"]
        this.allowanceMainEntryScreenAmountsTextBox = this.page.locator("(//input[@name='allowanceAmt'])[1]");
    this.updateAllDivisionsButton = this.page.getByRole("button", {
      name: "Update All",
    });
    this.saveAllChangesButton = this.page.getByRole("button", {
      name: "Save All Changes",
    });
    
    this.saveAndCreateAllowancesButton = this.page.getByRole("button", {
      name: "Save & Create Allowance(s)",
    });
    this.continueToWarehouseDatesButton = this.page.getByRole("button", {
      name: "Continue to Warehouse Dates",
    });

    this.reviewOverlapsAndEnterAmountsButton = this.page.getByRole("button", {
      name: "Review Overlaps & Enter Amounts",
    });


    this.editviewallitembutton=this.page.getByRole("button",{
      name:"Edit/View All Items",
    });
    this.allowanceToBeCreatedFieldsValue = this.page.locator(
      '(//div[@id="abs-stepper-preview-header1"]/div/div/span)[2]'
    );
    this.viewOfferDealSheetButton = this.page.getByRole("button", {
      name: "View Offer Deal Sheet",
    });
    this.viewSummaryButton = this.page.getByText("View Summary");
    this.headerFlatAllowanceAmountTextBox = this.page.locator(
     'xpath=//input[@id="abs-input-text-input"]'
    );
    this.continueToBillingDetails = this.page.getByRole("button", {
      name: "Continue to Billing Details",
    });
    this.headerFlatAllowanceTypeValue = this.page.locator(
      '(//div[@id="abs-stepper-preview-header1"]/div/div/span)[1]'
    );
    this.viewSummaryModalHeader = this.page.getByText(
      "Summary: Allowance Amounts |"
    );
    this.shipStore = this.page.getByText("Ship To Store");
    this.oneAllowanceWarehouseDSDradioOption = this.page
      .locator("label")
      .filter({ hasText: "One Allowance: Warehouse, DSD" })
      .getByTestId("radio-check");
    this.separateAllowancesByDSDradioOption = this.page
      .locator("label")
      .filter({ hasText: "Separate Allowances By DSD" })
      .getByTestId("radio-check");
    this.defaultSelectValueDisplayInPerformanceDropdown = this.page.getByRole(
      "button",
      { name: "Select" }
    );
    this.fourUEventPerformanceValue = this.page.getByText("4U Event (52)");
    this.addAnotherOfferButtonLocator = this.page.getByRole("button", {
      name: "+ Add Another Offer & Allowance",
    });
    this.addEvent = this.page.getByRole("link", { name: "Add Event" });
    this.createEventLocator = this.page.locator("//div[text()='Create Event']");
    this.getStartedNational = this.page.getByRole("button", {
      name: "Get Started",
    });
    this.getStartedDivisionPromotion = this.page.getByRole("button", {
      name: "Get Started",
    });
    this.getStartedAllowanceOnly = this.page.getByRole("button", {
      name: "Get Started",
    });
    this.ppgDropdown = this.page.locator("#abs-input-auto-complete");
    this.searchPPG = this.page.getByRole("textbox", {
      name: "Search Promo Product Groups",
    });
    this.saveEventDetails = this.page.getByRole("button", {
      name: "Save Event Details & Add",
    });
    this.removeInvalidDivisions = this.page.getByRole("button", {
      name: "Remove Invalid Divisions",
    });
    this.eventId = this.page.getByText("ID #");
    this.selectsVehicleType = this.page.getByRole("button", {
      name: "Vehicle Type/Custom Date",
    });
    this.selectStartWeekVehicle = this.page.getByRole("button", {
      name: "Start Week/Vehicle",
    });
    this.mutivendorCheckbox = this.page
      .getByTestId("checkbox-id")
      .locator("div")
      .nth(1);
    this.leadDistributorCheckbox = this.page
      .locator("label")
      .filter({ hasText: "Lead Distributor(s) Only" })
      .getByTestId("radio-check");
    this.leadDistributor1 = this.page
      .locator("#abs-accordion-container-body-wrapper-24")
      .getByTestId("checkbox-id")
      .locator("div")
      .nth(1);
    this.leadDistributor2 = this.page
      .locator("#abs-accordion-container-body-wrapper-29")
      .getByTestId("checkbox-id")
      .locator("div")
      .nth(1);
    this.confirm = this.page.getByRole("button", { name: "Confirm" });
    this.viewSummary = this.page
      .locator("span")
      .filter({ hasText: "View Summary" })
      .getByRole("img");
    this.socal = this.page.getByText("- Socal");
    this.EditDivisionBillingInfo = this.page.getByRole("button", {
      name: "Edit Division Billing Info",
    });
    this.haggen = this.page
      .locator("#abs-accordion-container-body-wrapper-24")
      .getByTestId("tag-id");
    this.socalBilling = this.page
      .locator("#abs-accordion-container-body-wrapper-29")
      .getByTestId("tag-id");
    this.expandAll = this.page.getByRole("button", { name: "Expand All" });
    this.editDivisionDateLocator = this.page.getByRole("button", {
      name: "Edit Divison Dates",
    });
    this.unitListCostLocator = this.page.locator(
      "table tr:nth-of-type(3) td:nth-of-type(7) input"
    );
    this.unitCostWithAllowLocator = this.page.locator(
      "table tr:nth-of-type(3) td:nth-of-type(8) input"
    );
    this.costLocator = this.page.locator("table th", {
      hasText: "Unit List Cost",
    });
    this.masterCostLocator = this.page.locator("table th", {
      hasText: "Unit Cost w/Allow",
    });
    this.listCostForMyVendorLocator = this.page.locator("th", {
      hasText: /^Unit List Cost$/,
    });
    this.masterCostForMyVendorLocator = this.page.locator("th", {
      hasText: /^Unit Cost w\/Allow$/,
    });
    this.dataVisibilityForMyVendor = this.page
      .locator("td")
      .filter({ hasText: /\$\d+/ })
      .first();
    this.offerStatusLocator = this.page
      .locator('//div[@data-testid="tag-id"]//span')
      .first();
    this.offerTabLocator = this.page.locator(
      '//*[@id="abs-event-creation-container"]/section/section[1]/div/div/div/div/span[2]/span'
    );
    this.divisionsSelectedTextLocator = this.page.getByRole("button", {
      name: "Divisions selected",
    });
    this.offersStatusesLocator = this.page.locator('div[data-testid="tag-id"]');
        this.cancelButtonLocator = this.page.locator("(//div[@id='abs-edit-and-remove-buttons-container1']/span)[2]");
        this.editButtonLocator = this.page.locator("(//div[@id='abs-edit-and-remove-buttons-container1']/span)[1]");
        this.modalLocator = this.page.getByTestId("modal-id");
        this.loaderLocator = this.page.getByRole('spinbutton');  }

  // This function verifies that the "New Offer" header is visible on the page.
  async verifyNewOfferHeader() {
    if (await this.offerHeader.isVisible()) {
      await this.offerHeader.waitFor({ state: "visible", timeout: 10000 });
      await expect(this.offerHeader).toBeVisible();
    }
  }

  // This function clicks the "Allowance Type" dropdown in the first stepper of the allowance workflow.
  async clickAllowanceTypeDropdown() {
    await this.allowanceTypeDropdown.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.allowanceTypeDropdown.click();
  }

  // This function selects the allowance type from the dropdown in the first stepper of the allowance workflow.
  async selectAllowanceType(allowanceType: string) {
    const allowance = this.page.getByText(allowanceType);
    if (await allowance.isVisible()) {
      await allowance.click();
    }
  }

  // This function ensures default selection of, or selects the radio button option in the first stepper of the allowance workflow based on the PPG source and label radio button option.
  async ensureDefaultSelectionAtOrSelectStepperOneRadioButtonOptionForCaseOffer(
    ppgSource: string,
    labelRadioButtonOption: string
  ) {
    await this.page.waitForLoadState("domcontentloaded");
    if (
      await this.allowanceToBeCreatedContentInputLabelForCaseOfferFlow.isVisible()
    ) {
      const labelText =
        await this.allowanceToBeCreatedContentInputLabelForCaseOfferFlow.innerText();
      console.log("labelText>", labelText);
    }

    if (ppgSource === "DSD") {
      if (
        labelRadioButtonOption ===
          NationalEventsOfferCreationSectionPageEnum.OneAllowanceDSDCombined ||
        labelRadioButtonOption ===
          NationalEventsOfferCreationSectionPageEnum.SeparateAllowancesByDSDDistributor
      ) {
        await this.page.getByText(labelRadioButtonOption).click();
      }
      await expect(this.allowanceDatesHeader).toBeVisible();
    } else if (
      ppgSource === NationalEventsOfferCreationSectionPageEnum.Warehouse
    ) {
      const radio = this.warehouseOnlyRadioButtonDefaultCheckedAndDisabled;
      const isChecked = await radio.isChecked();
      if (!isChecked) {
        await radio.click();
      }
      await expect(this.warehouseAllowanceDatesHeader).toBeVisible();
    } else if (
      ppgSource === NationalEventsOfferCreationSectionPageEnum.WarehouseDSD
    ) {
      if (
        labelRadioButtonOption ===
        NationalEventsOfferCreationSectionPageEnum.Both
      ) {
        await this.bothRadioButton.click();
        await expect(this.dsdAllowanceDatesHeader).toBeVisible();
      } else if (
        labelRadioButtonOption ===
        NationalEventsOfferCreationSectionPageEnum.WarehouseOnly
      ) {
        await this.warehouseOnlyRadioButton.click();
        await expect(this.warehouseAllowanceDatesHeader).toBeVisible();
      } else if (
        labelRadioButtonOption ===
        NationalEventsOfferCreationSectionPageEnum.DSDOnly
      ) {
        await this.dsdOnlyRadioButton.click();
        await expect(this.dsdAllowanceDatesHeader).toBeVisible();
      }
    }
  }

  // This function clicks the "Enter amounts" button that appears in one of the steppers of the allowance workflow.
  async clickEnterAmountsButton() {
      await this.page.waitForLoadState("domcontentloaded");
      if (await this.enterAmountsButton.isVisible()) {
        await this.enterAmountsButton.click();
    }
  }
  async clickEditViewAllItemsButton() {
    // Scroll to make the button visible
    await this.editviewallitembutton.scrollIntoViewIfNeeded();
    
    // Wait for the button to be visible
    await this.editviewallitembutton.waitFor({
        state: "visible",
        timeout: 10000,
    });
    
    // Click the button
    await this.editviewallitembutton.click();
}
    
  

  // This function clicks the "OK" button on the one-time popup message that appears over the allowance workflow's main entry screen on first entry of the allowance amount for any shared item during the session (create or edit flow).
  async clickOkButtonOnOneTimePopupMessage(sharedItem: boolean) {
    if (sharedItem) {
      await this.okButtonOnAllowAmountMainEntryScreenOneTimePopupMessage.waitFor(
        { state: "visible", timeout: 10000 }
      );
      await this.okButtonOnAllowAmountMainEntryScreenOneTimePopupMessage.click();
    }
  }

    // This function enters the allowance amount on the main entry screen of the allowance workflow.
    async enterAllowanceAmountOnMainEntryScreen(amount: string) {   // Needs to be removed while deleting playwright files
        await this.allowanceMainEntryScreenAmountsTextBox.fill(amount);
    }

  // This function clicks the "Update All Divisions" button on the main entry screen of the allowance workflow.
  async clickUpdateAllDivisionsButton() {
    if (await this.updateAllDivisionsButton.isVisible()) {
      await this.updateAllDivisionsButton.click();
    }
  }

  // This function clicks the "Save All Changes" button on the main entry screen of the allowance workflow.
  async clickSaveAllChangesButton() {
    if (await this.saveAllChangesButton.isVisible()) {
      await this.saveAllChangesButton.click();
    }
  }

  // This function waits for the event edit page to load after saving the event details.
  async waitForEventEditPage() {
    await this.page.waitForURL("**/events/edit/**");
  }

  // This function clicks the "Continue to Warehouse Dates" button that appears in one of the steppers of the allowance workflow.
  async clickContinueToWarehouseDatesButton() {
    await this.continueToBillingDetails.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.continueToBillingDetails.click();
  }

  // This function clicks the "Review Overlaps & Enter amounts" button that appears in one of the steppers of the allowance workflow.
  async clickReviewOverlapsAndEnterAmountsButton() {
    await this.reviewOverlapsAndEnterAmountsButton.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.reviewOverlapsAndEnterAmountsButton.click();
  }

  // This function clicks the "Save & Create Allowance(s)" button in the last stepper of the allowance workflow.
  async clickSaveAndCreateAllowancesButton() {
    await this.saveAndCreateAllowancesButton.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.saveAndCreateAllowancesButton.click();
  }

  // Depending on the PPG source and label radio button option, this function validates the "Allowance to be Created" field value in the preview header.
  async allowanceToBeCreatedFieldsValueValidation(
    ppgSource: string,
    labelRadioButtonOption: string
  ) {
    if (
      (ppgSource === NationalEventsOfferCreationSectionPageEnum.DSD &&
        labelRadioButtonOption ===
          NationalEventsOfferCreationSectionPageEnum.OneAllowanceDSDCombined) ||
      (ppgSource === NationalEventsOfferCreationSectionPageEnum.DSD &&
        labelRadioButtonOption ===
          NationalEventsOfferCreationSectionPageEnum.SeparateAllowancesByDSDDistributor) ||
      (ppgSource === NationalEventsOfferCreationSectionPageEnum.WarehouseDSD &&
        labelRadioButtonOption ===
          NationalEventsOfferCreationSectionPageEnum.Both) ||
      (ppgSource === NationalEventsOfferCreationSectionPageEnum.WarehouseDSD &&
        labelRadioButtonOption ===
          NationalEventsOfferCreationSectionPageEnum.WarehouseOnly) ||
      (ppgSource === NationalEventsOfferCreationSectionPageEnum.WarehouseDSD &&
        labelRadioButtonOption ===
          NationalEventsOfferCreationSectionPageEnum.DSDOnly)
    ) {
      const spanText = await this.allowanceToBeCreatedFieldsValue.innerText();
      expect(spanText.trim()).toBe(labelRadioButtonOption);
    } else if (
      ppgSource === NationalEventsOfferCreationSectionPageEnum.Warehouse
    ) {
      const spanText = await this.allowanceToBeCreatedFieldsValue.innerText();
      expect(spanText.trim()).toBe("Warehouse Only");
    }
  }

  async configureAllowance(
    option:
      | NationalEventsOfferCreationSectionPageEnum.CombinedDSD
      | NationalEventsOfferCreationSectionPageEnum.SeparateAllowancesByDSDDistributor,
    amount: string = "1"
  ) {
    try {
      console.log(`Configuring allowance with option: ${option}`);
      await this.allowanceTypeDropdown.isVisible();
      await this.allowanceTypeDropdown.click();

      if (await this.shipStore.isVisible()) {
        await this.shipStore.click();
      }
      if (option === NationalEventsOfferCreationSectionPageEnum.CombinedDSD) {
        await this.combinedDsdRadioButton.click();
      } else if (
        option ===
        NationalEventsOfferCreationSectionPageEnum.SeparateAllowancesByDSDDistributor
      ) {
        await this.separateAllowancesByDsdRadioButton.click();
      } else {
        throw new Error(`Invalid option provided: ${option}`);
      }

      await this.enterAmountsButton.click();
      await this.allowanceMainEntryScreenAmountsTextBox.fill(amount);
      await this.updateAllDivisionsButton.click();
      await this.saveAllChangesButton.click();
      await this.saveAndCreateAllowancesButton.click();

      console.log("Allowance configuration completed.");
    } catch (error) {
      console.error("Error during allowance configuration:", error);
      throw error;
    }
  }
  async viewOfferDealSheet() {
    await this.viewOfferDealSheetButton.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.viewOfferDealSheetButton.click();
  }

  async clickViewSummaryIcon() {
    await this.viewSummaryButton.waitFor({ state: "visible", timeout: 10000 });
    await this.viewSummaryButton.click();
  }

  async clickHeaderFlatAllowanceAmountTextBox() {
    await this.headerFlatAllowanceAmountTextBox.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.headerFlatAllowanceAmountTextBox.click();
  }

  async enterHeaderFlatAllowanceAmount(amount: string) {
    await this.headerFlatAllowanceAmountTextBox.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.headerFlatAllowanceAmountTextBox.fill(amount);
  }

  async clickContinueToBillingDetailsButton() {
    await this.continueToBillingDetails.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.continueToBillingDetails.click();
  }

  async verifyHeaderFlatOffersAllowanceType() {
    // Wait for the element containing the allowance type to be visible
    const allowanceTypeElement = this.headerFlatAllowanceTypeValue;
    await expect(allowanceTypeElement).toBeVisible();
    const allowanceType = await allowanceTypeElement.innerText();
    expect(allowanceType).toContain("Header Flat");
  }

  async createScanAllowance(dsdType: string): Promise<void> {
    await this.allowanceTypeDropdown.click();
    await this.selectAllowanceType("Scan");
    await this.defaultSelectValueDisplayInPerformanceDropdown.click();
    await this.fourUEventPerformanceValue.click();

    if (dsdType === NationalEventsOfferCreationSectionPageEnum.One) {
      await expect(this.oneAllowanceWarehouseDSDradioOption).toBeVisible();
      await this.oneAllowanceWarehouseDSDradioOption.click();
    } else if (
      dsdType === NationalEventsOfferCreationSectionPageEnum.Separate
    ) {
      await expect(this.separateAllowancesByDSDradioOption).toBeVisible();
      await this.separateAllowancesByDSDradioOption.click();
    } else {
      throw new Error(`Invalid DSD type: ${dsdType}`);
    }

    await this.enterAmountsButton.click();
    await this.allowanceMainEntryScreenAmountsTextBox.isVisible();
    await this.allowanceMainEntryScreenAmountsTextBox.fill("1");
    await this.updateAllDivisionsButton.click();
    await this.saveAllChangesButton.click();
    await this.saveAndCreateAllowancesButton.click();
  }

  async verifyAllowanceAmountIsVisible() {
    await expect(this.viewSummaryModalHeader).toBeVisible();
  }

  async userClicksOnAddEvent() {
    await this.addEvent.click();
  }
  async userClicksOnCreateEvent() {
    await this.createEventLocator.click();
  }

  async userClicksOnGetStarted() {
    await this.getStartedNational.nth(2).click();
  }
  async userClicksOnGetStartedForDivisionPromotion() {
    await this.getStartedDivisionPromotion.nth(0).click();
  }
  async userClicksOnGetStartedForAllowanceOnly() {
    await this.getStartedAllowanceOnly.nth(1).click();
  }
  
  

 

  async userClicksOnPPG(ppgValue: string) {
    await this.searchPPG.fill(ppgValue);
    await this.page.getByText(ppgValue).click();
    await this.searchPPG.fill(ppgValue);
  }

  async userDeSelectsDivision(division: string) {
    await this.divisionsSelectedTextLocator.click();
    await this.page
      .locator("div")
      .filter({ hasText: new RegExp(`^${division}.*`) })
      .locator("path")
      .click();
    await this.divisionsSelectedTextLocator.click();
    await this.page.waitForTimeout(20000);
  }

  async userSelectsVehicleType(vehicleType: string) {
    await this.selectsVehicleType.click();
    await this.page.getByText(vehicleType).click();
  }

  async userSetsEventDate(eventDate: string) {
    this.page.waitForTimeout(3000);
    let eventDateString: string = getVehicleInsertValueString(eventDate);
    await this.selectStartWeekVehicle.click();
    await this.page.getByTestId('popper').getByRole('textbox', { name: 'Search' }).fill(eventDateString);
    await this.page.getByText(eventDateString, { exact: false }).first().click();
  }

  async verifyEventDetailsAreSaved() {
    await expect(this.eventId).toBeVisible();
    await this.page.waitForTimeout(3000);
  }

  async userSaveEventDetails() {
    
    
     await this.saveEventDetails.click();
    
    
      
    
  
  };

  

  // filepath: /Users/<USER>/Downloads/MEUPP-E2E/src/ui/PageObjectModel/OfferCreation/NationalEventsOfferCreationSectionPage.ts
  async userRemoveInvalidDivisions() {
    /*const removeButton = this.page.getByRole('button', { name: 'Remove Invalid Divisions' });
  
    // boolean flag
    let isButtonVisible = false;
  
    try {
      isButtonVisible = await removeButton.isVisible({ timeout: 3000 }); // short timeout
    } catch {
      isButtonVisible = false; // button not found in 3s
    }
  
    if (isButtonVisible) {
      console.log("✅ Remove Invalid Divisions button found, clicking...");
      await removeButton.click();
    } else {
      console.log("ℹ️ No Invalid Divisions to remove, skipping...");
    }*/
   await this.removeInvalidDivisions.click();
  }

  async UserShouldClickOnTheCheckboxOfTheRadioButton() {
    await this.mutivendorCheckbox.click();
  }

  async VerifyTheVisibilityOfVendor(myVendor: string, otherVendor: string) {
    let myVendorLocator = this.page.getByRole("heading", { name: myVendor });
    let otherVendorLocator = this.page.getByRole("heading", {
      name: otherVendor,
    });
    await expect(myVendorLocator).toBeVisible();
    await expect(otherVendorLocator).toBeVisible();
  }

  async UserShouldSelectTheLeadDistributor() {
    await this.leadDistributorCheckbox.click();
  }

  async UserShouldSelectTheLeadDistributorCheckbox() {
    await this.leadDistributor1.click();
    await this.leadDistributor2.click();
  }

    async UserWillClickOnConfirm(actionLabel: string) {
        await this.page.getByRole('button', { name: actionLabel }).click();
    }

    
  async UserWillClickOnViewSummary() {
    await this.viewSummary.click();
  }

  async VerifyTheLeadDistributorInSummaryPage() {
    const lead1 = this.page.getByText("Lead").first();
    const lead2 = this.page.getByText("Lead").nth(1);

    await expect(lead1).toBeVisible();
    await expect(lead2).toBeVisible();
  }

  async UserWillCheckTheLeadDistributorForHaggendivision() {
    const fieldLocator = this.page
      .getByTestId("tag-id")
      .getByText(NationalEventsOfferCreationSectionPageEnum.Lead);
    await expect(fieldLocator).toBeVisible();
  }

  async UserWillCheckTheLeadDistributorForSocaldivision() {
    await this.socal.click();
    const fieldLocator = this.page
      .getByTestId("tag-id")
      .getByText(NationalEventsOfferCreationSectionPageEnum.Lead);
    await expect(fieldLocator).toBeVisible();
  }

  async UserWillCheckOnEditDivisionBillingInfo() {
    await this.EditDivisionBillingInfo.click();
  }

  async VerifyTheLeadForHaggen() {
    await this.page.evaluate(() =>
      window.scrollTo(0, document.body.scrollHeight)
    );
    await expect(this.haggen).toBeVisible();
  }

  async VerifyTheLeadForSocal() {
    await expect(this.socalBilling).toBeVisible();
  }

  async UserClickOnExpandAll() {
    await this.expandAll.click();
  }

  async clickOnAllowanceTypeDropdown() {
    this.allowanceTypeDropdown.click();
  }

  async selectOption(option: string) {
    console.log("clicking on " + option);
    await this.page
      .locator("div")
      .filter({ hasText: new RegExp(`^${option}$`) }) // exact match, case-sensitive
      .nth(2)
      .click();
    await this.page.waitForTimeout(3000);
  }

  async selectPerformanceOption(option: string) {
    const selectButton = this.page.locator("//div[@data-testid='abs-allowance-type-performance-input-performance-field']//div[@id='abs-input-select-container']//button");
    if (await selectButton.isVisible()) {
      await selectButton.click();

      // Escape special characters in the option string for regex
      const escapedOption = option.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
      await this.page
        .locator("div")
        .filter({ hasText: new RegExp(`^${escapedOption}$`) })
        .nth(2)
        .click();
    } else {
      console.log(
        `Only one performance type available. No selection needed for "${option}".`
      );
    }
  }

  async OneAllowanceRadioButton() {
    await this.page.waitForTimeout(3000);
    await this.page
      .locator("label")
      .filter({ hasText: "One Allowance: DSD Combined" })
      .getByTestId("radio-check")
      .click();
    await this.page.waitForLoadState('networkidle');
  }

  async UserClickOnEnterAmount() {
    await this.enterAmountsButton.scrollIntoViewIfNeeded();
    await this.enterAmountsButton.click();
  }

  async UserUpdateTheAllowanceAmount() {
    await this.allowanceMainEntryScreenAmountsTextBox.click();
    await this.page.waitForTimeout(3000);
    await this.allowanceMainEntryScreenAmountsTextBox.fill("1");
  }

  async UserSelectTheRadioButtonOfSeparateAllowance() {
    await this.page.waitForTimeout(3000);
    await this.page
      .locator("label")
      .filter({ hasText: "Separate Allowances By DSD" })
      .getByTestId("radio-check")
      .click();
    await this.page.waitForTimeout(3000);
  }

    async userClicksUpdateAllFieldsTextBoxInAllowanceAmountsMainEntryScreen() {
        await this.loaderLocator.waitFor({ state: 'hidden', timeout: 30000 });
        await this.page.waitForLoadState('networkidle');
        const element = this.allowanceMainEntryScreenAmountsTextBox;
        await element.waitFor({ state: 'visible', timeout: 40000 });
        await element.click();
    }

    async userEntersAllowanceAmountToUpdateAllFieldsTextBoxInAllowanceAmountsMainEntryScreen(amount: string) {
        await this.page.waitForLoadState('networkidle');
        const element = this.allowanceMainEntryScreenAmountsTextBox;
        await element.waitFor({ state: 'visible', timeout: 3000 });
        await element.fill(amount);
        await this.page.waitForTimeout(10000);
    }

    async userValidatesTheDisplayOfTheAllowanceAmountEnteredToTheUpdateAllFieldsTextBoxInAllowanceAmountsMainEntryScreen(amount: string) {
        const finalValue = await this.allowanceMainEntryScreenAmountsTextBox.inputValue();
        expect(finalValue).toBe(amount);
    }

  async UserClickOnSaveAllChanges() {
    await this.saveAllChangesButton.click();
  }

  async UserClickOnSaveAndCreateAllowance() {
    await this.saveAndCreateAllowancesButton.click();
  }

  async VerifyTheValueInPreviewHeader() {
    await this.allowanceToBeCreatedFieldsValue.isVisible();
  }

  async UpdateAllDivisionsButton() {
    await this.updateAllDivisionsButton.click();
  }

  async UserVerifyTheLeadSelection() {
    await this.confirm.click();
  }

  async UserClickOnEditDivisionDates() {
    await this.page.evaluate(() =>
      window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" })
    );
    await this.editDivisionDateLocator.click();
    await this.page.evaluate(() =>
      window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" })
    );
  }

  async VerifyTheGrayedOutFieldForMultivendorppg() {
    await expect(this.costLocator).toBeHidden();
    await expect(this.masterCostLocator).toBeHidden();
  }

  async VerifyUserShouldNotSeeListCostMasterCost() {
    await expect(this.costLocator).toHaveCount(0);
    await expect(this.masterCostLocator).toHaveCount(0);
  }

  async clickAddAnotherOffer() {
    await this.page.waitForLoadState("domcontentloaded");
    await this.addAnotherOfferButtonLocator.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.addAnotherOfferButtonLocator.click();
    await this.page.waitForLoadState("networkidle");
    await this.offerHeader.waitFor({ state: "visible", timeout: 10000 });
    await this.page.waitForTimeout(2000);
  }

  async VerifyTheDistributorList() {
    const distributorInfoList = await mongoDBComponent.getAttributeValueList();
    if (distributorInfoList.length > 0) {
      for (const distributor of distributorInfoList) {
        const distributorLocator = this.page.getByText(distributor);
        await expect(distributorLocator).toBeVisible();
      }
    } else {
      console.log("No distributors found in the database.");
    }
  }

  async VerifyUserShouldSeeListCostMasterCostForMyVendors() {
    await expect(this.listCostForMyVendorLocator).toBeVisible();
    await expect(this.masterCostForMyVendorLocator).toBeVisible();
    await expect(this.dataVisibilityForMyVendor).toBeVisible();
  }
  async VerifyUserShouldClickOnSocalDivisionForMultiVendor() {
    await this.socal.click();
  }
  async verifyOfferStatus(expectedStatus: string): Promise<void> {
    await this.page.waitForTimeout(2000);

    const statusElement = this.offerStatusLocator;
    if (!statusElement) {
      throw new Error("Status element not found");
    }

    const actualStatus = await statusElement.innerText();
    console.log(`Found status text: "${actualStatus}"`);

    if (
      actualStatus.trim().toLowerCase() !== expectedStatus.trim().toLowerCase()
    ) {
      await this.page.screenshot({ path: "offer-status-error.png" });
      throw new Error(
        `Status mismatch - Expected: "${expectedStatus}", Found: "${actualStatus}"`
      );
    }
  }
  async verifyCanceledOrRejectedStatusInOfferTabSection(
    expectedStatus: string
  ): Promise<void> {
    try {
      await this.page.waitForLoadState("networkidle");
      await this.offerTabLocator.waitFor({ state: "visible", timeout: 20000 });
      const box = await this.offerTabLocator.boundingBox();
      if (box) {
        await this.page.mouse.move(
          box.x + box.width / 2,
          box.y + box.height / 2
        );
        await this.page.waitForTimeout(500);
      }

      // Force click the element
      await this.page.evaluate((selector) => {
        const element = document.evaluate(
          selector,
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null
        ).singleNodeValue as HTMLElement;
        if (element) {
          element.click();
        }
      }, '//*[@id="abs-event-creation-container"]/section/section[1]/div/div/div/div/span[2]/span');

      // Wait for content to load
      await this.page.waitForLoadState("networkidle");
      await this.page.waitForTimeout(2000); // Short stabilization period

      // Look for status tags in the offers section
      console.log(`Looking for status: "${expectedStatus}"`);
      await this.offersStatusesLocator
        .first()
        .waitFor({ state: "visible", timeout: 10000 });

      // Get all status tags and check their text
      const statusElements = await this.offersStatusesLocator.all();
      console.log(`Found ${statusElements.length} status elements`);

      for (const element of statusElements) {
        const text = await element.innerText();
        console.log(`Found status: "${text}"`);

        if (text.trim().toLowerCase() === expectedStatus.toLowerCase()) {
          console.log(`✓ Found matching status: "${text}"`);
          return;
        }
      }

      // If we get here, we didn't find the status
      console.error(`Status "${expectedStatus}" not found`);
      await this.page.screenshot({
        path: "offer-status-not-found.png",
        fullPage: true,
      });
      throw new Error(
        `Status "${expectedStatus}" not found in the offer section`
      );
    } catch (error: any) {
      console.error("Error in verifyStatusInOfferTabSection:", error);
    }
  }
  async clickContinueToBillingDetails() {
    await this.continueToBillingDetails.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.continueToBillingDetails.click();
  }

  async clickHeaderFlatAllowanceAmountTextBoxDefault() {
    await this.headerFlatAllowanceAmountTextBox.waitFor({
      state: "visible",
      timeout: 10000,
    });
    await this.headerFlatAllowanceAmountTextBox.click();
    await this.page.waitForTimeout(1000); // Small delay to ensure input is ready
  }

  async enterHeaderFlatAllowanceDefaultAmount() {
    await this.headerFlatAllowanceAmountTextBox.fill("1");
  }

  async UserClickOnUpdateOffer() {
    await this.page.getByRole("button", { name: "Update Offer" }).click();
  }
  async clickActionButton(actionType: string): Promise<void> {
    let buttonLocator: Locator;

    switch (actionType.toLowerCase()) {
      case 'cancel':
      case 'remove':
        buttonLocator = this.cancelButtonLocator;
        break;
      case 'edit':
        buttonLocator = this.editButtonLocator;
        break;
      default:
        throw new Error(`Unsupported action type: ${actionType}`);
    }

    await buttonLocator.waitFor({
      state: "visible",
      timeout: 5000,
    });
    await buttonLocator.click();
  }

    async verifyConfirmationModalVisible(): Promise<void> {
        await this.modalLocator.waitFor({ state: "visible", timeout: 5000 });
        await expect(this.modalLocator).toBeVisible();
    }
    async verifyConfirmationMessageVisible(): Promise<void> {
        await expect(
            this.page.getByText("Are you sure you want to")
        ).toBeVisible();
    }
    async selectRadioButtonInTheFirstStepperOfAllowanceWorkflowSection(radioButtonLabel: string) {
        await this.page.waitForTimeout(3000);
        await this.page.locator('label').filter({ hasText: radioButtonLabel }).getByTestId('radio-check').click();
        await this.page.waitForLoadState('networkidle');
    }
    async scrollToEditViewAllItemsButton() {
      const editViewAllItemsButton = this.page.getByRole('button', { name: 'Edit/View All Items' });
      await editViewAllItemsButton.scrollIntoViewIfNeeded();
    }
    
    async waitForEditViewAllItemsButtonVisible() {
      const editViewAllItemsButton = this.page.getByRole('button', { name: 'Edit/View All Items' });
      await editViewAllItemsButton.waitFor({ state: 'visible', timeout: 10000 });
    }
    
    
    
}

