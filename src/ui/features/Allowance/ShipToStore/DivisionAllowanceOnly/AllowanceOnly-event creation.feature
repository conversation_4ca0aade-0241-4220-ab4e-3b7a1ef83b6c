Feature: Alowance only event creation
@UI-manju1
 Scenario Outline: Verify the creation of a Allowanceonly Event for a Vendor by passing DSD PPG

  Given "National Vendor" logs into MEUPP application
  #When User clicks on 'Create Event' in the 'Promotion Management' page
   When User clicks on 'Add event'
   When User select the event type "Allowance Only"
   And User populates the product group with "<PPG>"
     When User selects vehicle type "<vehicleType>"
     And User sets event date as "<eventDate>"
     When User save the event details
    Then Verify the user can save the event details successfully
      Examples:
    | vehicleType   | eventDate | PPG   |
    | Weekly Insert | FUTURE~15 | 25025 |
