Feature: Alowance only offer creation

Background:
    Given "National Vendor" logs into MEUPP application
    When User clicks on "Add event"
     When User select the event type "Allowance Only"
    

    
@UI-manju2
 Scenario Outline: Verify the creation of a Allowanceonly offer for a Vendor by passing DSD PPG
  And User populates the product group with "<PPG>"
     When User selects vehicle type "<vehicleType>"
     And User sets event date as "<eventDate>"
     When User save the event details
    Then Verify the user can save the event details successfully
    When User click on "Case"
 And User selects "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow section
    And user clicks on "review overlaps and enter amounts button"
    Then user clicks on 'Edit/View All Items button'
    And User clicks on Enter amounts
     And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
     When User click on Update All divisions
    And User click on Save all changes
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
     When User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page
       Examples:
    | vehicleType   | eventDate | PPG   |allowanceAmount |
    | Weekly Insert | FUTURE~15 | 188740 | 6              |


     #Scan Allowance
     Scenario Outline: Verify the creation offer for scan
     And User populates the product group with "<PPG>"
     When User selects vehicle type "<vehicleType>"
     And User sets event date as "<eventDate>"
     When User save the event details
    Then Verify the user can save the event details successfully
    And User click on "Scan"
    #And User selects the "4U Event (52)"
    And User selects "One Allowance: Warehouse, DSD, or Combined" radio button in the first stepper of the allowance workflow section
    And user clicks on "review overlaps and enter amounts button"
    Then user clicks on 'Edit/View All Items button'
     And User clicks on Enter amounts
     And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
     When User click on Update All divisions
    And User click on Save all changes
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
    When User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page

      Examples:
    | vehicleType   | eventDate | PPG   |allowanceAmount |
    | Weekly Insert | FUTURE~15 | 188740 | 6              |


    #Header Flat Allowance
  Scenario Outline: Verify the creation offer for headr flat
  And User populates the product group with "<PPG>"
     When User selects vehicle type "<vehicleType>"
     And User sets event date as "<eventDate>"
     When User save the event details
    Then Verify the user can save the event details successfully
 And User click on "Header Flat"
   And User selects the "Other (99)"
   And User clicks on the header flat allowance amount
   And User enters "<allowanceAmount>" to the header flat allowance amount text box in the first stepper of the allowance workflow section
   And User click on continue to Billing Details
   Then verify Allowance to be created field value in the preview header
  
      Examples:
    | vehicleType   | eventDate | PPG   |allowanceAmount |
    | Weekly Insert | FUTURE~15 | 188740 | 6              |