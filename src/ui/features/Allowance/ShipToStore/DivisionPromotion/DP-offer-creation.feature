Feature: DP Offer creation 

@UI-manju
 Scenario Outline: Verify the creation of a DivisionPromotion Event for a Vendor by passing DSD PPG
 Given "National Vendor" logs into MEUPP application
     When User clicks on 'Create Event' in the 'Promotion Management' page
     When User select the event type "Division Promotion"
     And User populates the product group with "<PPG>"
     When User selects vehicle type "<vehicleType>"
     And User sets event date as "<eventDate>"
     When User save the event details
    Then Verify the user can save the event details successfully

 # Case Allowance
    When User click on "Case"
    And User selects "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow section
    And user clicks on review overlaps and enter amounts button
    Then user clicks on 'Edit/View All Items button'
    

   

    


   
     
  

   

   
   
 
     Examples:
    | vehicleType   | eventDate | PPG   | allowanceAmount|
    | Weekly Insert | FUTURE~15 | 188740 |    1         |    