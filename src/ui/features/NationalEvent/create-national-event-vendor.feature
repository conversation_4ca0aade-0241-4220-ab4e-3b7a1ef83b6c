@UI-Regression
Feature: Create National Event for vendor
  @UI-20654
  Scenario: Verify the creation of a National Event for a Vendor by passing DSD PPG
    Given "National Vendor" logs into MEUPP application
   
    When User clicks on 'Create Event' in the 'Promotion Management' page
    When User select the event type "National"
    Then User populates the product group with "<PPG>"
    When User selects vehicle type "<vehicleType>"
    And User sets event date as "<eventDate>"
    When User save the event details
    When User Remove Invalid divisions if exists
    When User save the event details
    Then Verify the user can save the event details successfully
    Examples:
    | vehicleType   | eventDate | PPG   |
    | Weekly Insert | FUTURE~15 | 755664  |