import { After, AfterAll, Before, BeforeAll, BeforeStep, setDefaultTimeout } from '@cucumber/cucumber';
import { <PERSON><PERSON><PERSON>, BrowserContext, Page, chromium } from 'playwright';
import { pageFixture } from './pageFixtures';
import { mongoDBComponent } from '../src/ui/step-definitions/mongo-db/mongoContext';

let browser: Browser;
let context: BrowserContext;
let page: Page;

setDefaultTimeout(30 * 1000);

BeforeAll(async function () {
    browser = await chromium.launch({ headless: false });
});

Before(async function ({ pickle }) {
    console.log(`🚀 Starting scenario: ${pickle.name}`);

    context = await browser.newContext({
        viewport: { width: 1680, height: 1080 },
        ignoreHTTPSErrors: true,
        serviceWorkers: 'block'
    });
    page = await context.newPage();
    //@ts-ignore
    pageFixture.page = page;
});

BeforeStep(async function ({pickleStep }) {
   console.log(`Running Step: ${pickleStep.text}`);
});

After(async function ({ pickle, result }) {
    console.log(`✅ Finished scenario: ${pickle.name}`);
    // Optional: handle result.status or take screenshots
});

AfterAll(async function () {
    await browser.close();
    await mongoDBComponent.disconnect();
});

