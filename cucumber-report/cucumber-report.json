[{"description": "", "elements": [{"description": "", "id": "dp-offer-creation;scenario-outline:-verify-the-creation-of-offer-for-case", "keyword": "Scenario Outline", "line": 30, "name": "Scenario Outline: Verify the creation of offer for case", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 1201426960}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "\"National Vendor\" logs into MEUPP application", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:13"}, "result": {"status": "failed", "duration": 6856084535, "error_message": "page.goto: net::ERR_CONNECTION_ABORTED at https://memsp-qa2.albertsons.com/memsp-ui-shell/meupp/\nCall log:\n\u001b[2m  - navigating to \"https://memsp-qa2.albertsons.com/memsp-ui-shell/meupp/\", waiting until \"networkidle\"\u001b[22m\n\n    at attemptLogin (/Users/<USER>/Downloads/MEUPP-E2E/src/ui/Utils/loginHelper.ts:26:16)\n    at World.<anonymous> (/Users/<USER>/Downloads/MEUPP-E2E/src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:14:23)"}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "User clicks on \"Add event\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:16"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 7, "name": "User select the event type \"Division Promotion\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:22"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 11, "name": "User populates the product group with \"188740\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:41"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 12, "name": "User selects vehicle type \"Weekly Insert\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:46"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 13, "name": "User sets event date as \"FUTURE~15\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:50"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 14, "name": "User save the event details", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:54"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 15, "name": "Verify the user can save the event details successfully", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:62"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 16, "name": "User click on \"Case\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:8"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 17, "name": "User selects \"One Allowance: DSD Combined\" radio button in the first stepper of the allowance workflow section", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:197"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 18, "name": "user clicks on \"review overlaps and enter amounts button\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:209"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 19, "name": "user clicks on 'Edit/View All Items button'", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:209"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "User clicks on Enter amounts", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:24"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "User enters \"5\" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:37"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 22, "name": "User click on Update All divisions", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:54"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 23, "name": "User click on Save all changes", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:58"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 24, "name": "User click on Save and Create Allowance", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:62"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 25, "name": "verify Allowance to be created field value in the preview header", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:66"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 26, "name": "User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:192"}, "result": {"status": "skipped", "duration": 0}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 377978}}], "tags": [{"name": "@UI-manju12", "line": 9}], "type": "scenario"}, {"description": "", "id": "dp-offer-creation;scenario-outline:-verify-the-creation-of-offer-for-scan", "keyword": "Scenario Outline", "line": 53, "name": "Scenario Outline: Verify the creation of offer for scan", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 388389600}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "\"National Vendor\" logs into MEUPP application", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:13"}, "result": {"status": "failed", "duration": 60004971539, "error_message": "page.goto: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"https://memsp-qa2.albertsons.com/memsp-ui-shell/meupp/\", waiting until \"networkidle\"\u001b[22m\n\n    at attemptLogin (/Users/<USER>/Downloads/MEUPP-E2E/src/ui/Utils/loginHelper.ts:26:16)\n    at World.<anonymous> (/Users/<USER>/Downloads/MEUPP-E2E/src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:14:23)"}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "User clicks on \"Add event\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:16"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 7, "name": "User select the event type \"Division Promotion\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:22"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 34, "name": "User populates the product group with \"188740\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:41"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 35, "name": "User selects vehicle type \"Weekly Insert\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:46"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 36, "name": "User sets event date as \"FUTURE~15\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:50"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 37, "name": "User save the event details", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:54"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 38, "name": "Verify the user can save the event details successfully", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:62"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 39, "name": "User click on \"Scan\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:8"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 40, "name": "User selects the \"4U Event (52)\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:16"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 41, "name": "User selects \"One Allowance: Warehouse, DSD, or Combined\" radio button in the first stepper of the allowance workflow section", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:197"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 42, "name": "user clicks on review overlaps and enter amounts button", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:206"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 43, "name": "user clicks on 'Edit/View All Items button'", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:209"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 44, "name": "User clicks on Enter amounts", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:24"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 45, "name": "User enters \"5\" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:37"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 46, "name": "User click on Update All divisions", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:54"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 47, "name": "User click on Save all changes", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:58"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 48, "name": "User click on Save and Create Allowance", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:62"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 49, "name": "verify Allowance to be created field value in the preview header", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:66"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 50, "name": "User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:192"}, "result": {"status": "skipped", "duration": 0}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 375524}}], "tags": [], "type": "scenario"}, {"description": "", "id": "dp-offer-creation;scenario-outline:-verify-the-creation-of-offer-for-headerflat", "keyword": "Scenario Outline", "line": 86, "name": "Scenario Outline: Verify the creation of offer for headerflat", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 262409776}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "\"National Vendor\" logs into MEUPP application", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:13"}, "result": {"status": "passed", "duration": 48506290545}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "User clicks on \"Add event\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:16"}, "result": {"status": "passed", "duration": 158993153}}, {"arguments": [], "keyword": "When ", "line": 7, "name": "User select the event type \"Division Promotion\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:22"}, "result": {"status": "passed", "duration": 1933956727}}, {"arguments": [], "keyword": "And ", "line": 57, "name": "User populates the product group with \"188740\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:41"}, "result": {"status": "passed", "duration": 1834297409}}, {"arguments": [], "keyword": "When ", "line": 58, "name": "User selects vehicle type \"Weekly Insert\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:46"}, "result": {"status": "passed", "duration": 1224515755}}, {"arguments": [], "keyword": "And ", "line": 59, "name": "User sets event date as \"FUTURE~15\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:50"}, "result": {"status": "passed", "duration": 1353219963}}, {"arguments": [], "keyword": "When ", "line": 60, "name": "User save the event details", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:54"}, "result": {"status": "passed", "duration": 144152160}}, {"arguments": [], "keyword": "Then ", "line": 61, "name": "Verify the user can save the event details successfully", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:62"}, "result": {"status": "passed", "duration": 5902564179}}, {"arguments": [], "keyword": "And ", "line": 62, "name": "User click on \"Header Flat\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:8"}, "result": {"status": "passed", "duration": 3143874776}}, {"arguments": [], "keyword": "And ", "line": 63, "name": "User clicks on the header flat allowance amount", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:74"}, "result": {"status": "passed", "duration": 1042061460}}, {"arguments": [], "keyword": "And ", "line": 64, "name": "User enters \"5\" to the header flat allowance amount text box in the first stepper of the allowance workflow section", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:202"}, "result": {"status": "passed", "duration": 35617031}}, {"arguments": [], "keyword": "And ", "line": 65, "name": "User click on continue to Billing Details", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:159"}, "result": {"status": "passed", "duration": 119578284}}, {"arguments": [], "keyword": "And ", "line": 66, "name": "User click on Save and Create Allowance", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:62"}, "result": {"status": "passed", "duration": 3011727098}}, {"arguments": [], "keyword": "Then ", "line": 67, "name": "verify Allowance to be created field value in the preview header", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:66"}, "result": {"status": "passed", "duration": 2361212}}, {"arguments": [], "keyword": "When ", "line": 68, "name": "User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:192"}, "result": {"status": "passed", "duration": 3444445046}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 357390}}], "tags": [], "type": "scenario"}], "id": "dp-offer-creation", "line": 1, "keyword": "Feature", "name": "DP Offer creation", "tags": [], "uri": "src/ui/features/Allowance/ShipToStore/DivisionPromotion/DP-offer-creation.feature"}]