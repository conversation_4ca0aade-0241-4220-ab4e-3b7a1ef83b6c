[{"description": "", "elements": [{"description": "", "id": "dp-offer-creation;verify-the-creation-of-a-divisionpromotion-event-for-a-vendor-by-passing-dsd-ppg", "keyword": "Scenario Outline", "line": 34, "name": "Verify the creation of a DivisionPromotion Event for a Vendor by passing DSD PPG", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 945550832}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "\"National Vendor\" logs into MEUPP application", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:13"}, "result": {"status": "passed", "duration": 62549233188}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "User clicks on 'Create Event' in the 'Promotion Management' page", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:17"}, "result": {"status": "passed", "duration": 154198417}}, {"arguments": [], "keyword": "When ", "line": 7, "name": "User select the event type \"Division Promotion\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:23"}, "result": {"status": "passed", "duration": 5488000889}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "User populates the product group with \"188740\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:27"}, "result": {"status": "passed", "duration": 3375858755}}, {"arguments": [], "keyword": "When ", "line": 9, "name": "User selects vehicle type \"Weekly Insert\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:32"}, "result": {"status": "passed", "duration": 1068436572}}, {"arguments": [], "keyword": "And ", "line": 10, "name": "User sets event date as \"FUTURE~15\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:36"}, "result": {"status": "passed", "duration": 1341975778}}, {"arguments": [], "keyword": "When ", "line": 11, "name": "User save the event details", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:40"}, "result": {"status": "passed", "duration": 143054995}}, {"arguments": [], "keyword": "Then ", "line": 12, "name": "Verify the user can save the event details successfully", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:48"}, "result": {"status": "passed", "duration": 7910168251}}, {"arguments": [], "keyword": "When ", "line": 15, "name": "User click on \"Case\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:7"}, "result": {"status": "passed", "duration": 3140333685}}, {"arguments": [], "keyword": "And ", "line": 16, "name": "User selects \"One Allowance: DSD Combined\" radio button in the first stepper of the allowance workflow section", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:191"}, "result": {"status": "passed", "duration": 3105944099}}, {"arguments": [], "keyword": "And ", "line": 17, "name": "user clicks on review overlaps and enter amounts button", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:199"}, "result": {"status": "passed", "duration": 4685454907}}, {"arguments": [], "keyword": "When ", "line": 18, "name": "user clicks on Edit/View All Items button", "result": {"status": "undefined", "duration": 0}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 226529}}], "tags": [{"name": "@UI-manju", "line": 3}], "type": "scenario"}], "id": "dp-offer-creation", "line": 1, "keyword": "Feature", "name": "DP Offer creation", "tags": [], "uri": "src/ui/features/Allowance/ShipToStore/DivisionPromotion/DP-offer-creation.feature"}]