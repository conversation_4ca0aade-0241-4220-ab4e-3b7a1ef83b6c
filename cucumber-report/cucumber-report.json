[{"description": "", "elements": [{"description": "", "id": "dp-offer-creation;verify-the-creation-of-a-divisionpromotion-event-for-a-vendor-by-passing-dsd-ppg", "keyword": "Scenario Outline", "line": 65, "name": "Verify the creation of a DivisionPromotion Event for a Vendor by passing DSD PPG", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 1032841370}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "\"National Vendor\" logs into MEUPP application", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:13"}, "result": {"status": "passed", "duration": 44660034179}}, {"arguments": [], "keyword": "When ", "line": 7, "name": "User clicks on 'Add event'", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:16"}, "result": {"status": "passed", "duration": 203398303}}, {"arguments": [], "keyword": "When ", "line": 8, "name": "User select the event type \"Division Promotion\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:22"}, "result": {"status": "passed", "duration": 1958811635}}, {"arguments": [], "keyword": "And ", "line": 9, "name": "User populates the product group with \"188740\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:41"}, "result": {"status": "passed", "duration": 1816904240}}, {"arguments": [], "keyword": "When ", "line": 10, "name": "User selects vehicle type \"Weekly Insert\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:46"}, "result": {"status": "passed", "duration": 1139126823}}, {"arguments": [], "keyword": "And ", "line": 11, "name": "User sets event date as \"FUTURE~15\"", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:50"}, "result": {"status": "passed", "duration": 1082022390}}, {"arguments": [], "keyword": "When ", "line": 12, "name": "User save the event details", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:54"}, "result": {"status": "passed", "duration": 158363343}}, {"arguments": [], "keyword": "Then ", "line": 13, "name": "Verify the user can save the event details successfully", "match": {"location": "src/ui/step-definitions/event-creation/national/NationalEventCreationSteps.ts:62"}, "result": {"status": "passed", "duration": 5891876055}}, {"arguments": [], "keyword": "When ", "line": 16, "name": "User click on \"Case\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:8"}, "result": {"status": "passed", "duration": 3101183754}}, {"arguments": [], "keyword": "And ", "line": 17, "name": "User selects \"One Allowance: DSD Combined\" radio button in the first stepper of the allowance workflow section", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:197"}, "result": {"status": "passed", "duration": 3118641907}}, {"arguments": [], "keyword": "And ", "line": 18, "name": "user clicks on \"review overlaps and enter amounts button\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:214"}, "result": {"status": "failed", "duration": 813482, "error_message": "Error: Unknown button: review overlaps and enter amounts button\n    at World.<anonymous> (/Users/<USER>/Downloads/MEUPP-E2E/src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:258:14)"}}, {"arguments": [], "keyword": "Then ", "line": 19, "name": "user clicks on 'Edit/View All Items button'", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:214"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "User clicks on Enter amounts", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:24"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "User enters \"5\" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:37"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 22, "name": "User click on Update All divisions", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:54"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 23, "name": "User click on Save all changes", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:58"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 24, "name": "User click on Save and Create Allowance", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:62"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 25, "name": "verify Allowance to be created field value in the preview header", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:66"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 26, "name": "User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:192"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 28, "name": "User click on \"Scan\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:8"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 29, "name": "User selects the \"4U Event (52)\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:16"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 30, "name": "User selects \"One Allowance: Warehouse, DSD, or Combined\" radio button in the first stepper of the allowance workflow section", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:197"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 31, "name": "user clicks on review overlaps and enter amounts button", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:205"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 32, "name": "user clicks on 'Edit/View All Items button'", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:214"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 33, "name": "User clicks on Enter amounts", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:24"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 34, "name": "User enters \"5\" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:37"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 35, "name": "User click on Update All divisions", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:54"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 36, "name": "User click on Save all changes", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:58"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 37, "name": "User click on Save and Create Allowance", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:62"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 38, "name": "verify Allowance to be created field value in the preview header", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:66"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 39, "name": "User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:192"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 41, "name": "User click on \"Header Flat\"", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:8"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 42, "name": "User clicks on the header flat allowance amount", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:74"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 43, "name": "User enters \"5\" to the header flat allowance amount text box in the first stepper of the allowance workflow section", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:202"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 44, "name": "User click on continue to Billing Details", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:159"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 45, "name": "User click on Save and Create Allowance", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:62"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 46, "name": "verify Allowance to be created field value in the preview header", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:66"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "When ", "line": 47, "name": "User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page", "match": {"location": "src/ui/step-definitions/offer-creation/AllowanceFlowSteps.ts:192"}, "result": {"status": "skipped", "duration": 0}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 375683}}], "tags": [{"name": "@UI-manju", "line": 3}], "type": "scenario"}], "id": "dp-offer-creation", "line": 1, "keyword": "Feature", "name": "DP Offer creation", "tags": [], "uri": "src/ui/features/Allowance/ShipToStore/DivisionPromotion/DP-offer-creation.feature"}]